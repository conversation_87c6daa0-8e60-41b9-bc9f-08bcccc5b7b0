import React, { useEffect } from "react";
import { StatusBar } from "expo-status-bar";
import { View } from "react-native";
import ChatComponent from "../components/chat/ChatComponent";
import { useIsFocused } from "@react-navigation/native";

export default function FreeTalk({ route, navigation }) {
  const item = {
    _id: route?.params?.showVoiceAssistant
      ? "68d72028d62392d251409a46"
      : "67ba493b70ebab475c063a79",
    title: "free talk",
    scenario:
    "You are a friendly conversation partner whose goal is to engage users in a natural, free-flowing chat. Begin by inviting them to choose a topic or, if they prefer, suggest one yourself. Avoid mentioning that you are an AI or describing yourself in technical terms. Your opening should feel warm, dynamic, and open-ended—something that makes the user feel comfortable and eager to respond, without sounding robotic or overly formal.",
    freeTalkType: route?.params?.freeTalkType,
    freeTalkCustomScenario: route?.params?.freeTalkCustomScenario,
  };
  const isFocused = useIsFocused();

  useEffect(() => {
    navigation.getParent().setOptions({ tabBarStyle: { display: "none" } });
    return () =>
      navigation.getParent().setOptions({ tabBarStyle: { display: "flex" } });
  }, [navigation]);

  return (
    <>
      {isFocused && <StatusBar style="light" />}

      <ChatComponent
        route={{
          params: {
            topicItem: item,
            showVoiceAssistant: route?.params?.showVoiceAssistant,
          },
        }}
        navigation={navigation}
      />
    </>
  );
}
