import RequestHelper from "../../utils/requestHelper";
import { updateCoins } from "../../store/slices/authSlice";

/**
 * Generates a simple UUID when the uuid library fails
 */
export const simpleUUID = () => {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

/**
 * Reduces user gems when starting a new lesson/topic
 * Only reduces gems if user is not premium and has sufficient gems
 */
export const reduceGemsForNewLesson = async (
  scenarioId: string,
  isPremium: boolean,
  currentGems: number,
  dispatch: any
  // gemCostPerLesson: number = 25
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Check if user has sufficient gems
    if (currentGems >= 25) {
      const { gemCostPerLesson } = await RequestHelper(
        "post",
        "/users/gems/reduce",
        { scenarioId },
        null,
        null,
        false
      );
      dispatch(updateCoins(currentGems - gemCostPerLesson));
    }
    return { success: true };
  } catch (error) {
    console.error("Error reducing gems:", error);
    return {
      success: false,
      error: "Failed to reduce gems due to network error",
    };
  }
};

/**
 * Retrieves chat history from the server
 */
export const getChats = async (topicId: string) => {
  try {
    const { lastMessages, scores } = await RequestHelper(
      "get",
      `/ai/chats/?scenarioId=${topicId}`,
      null,
      null,
      null,
      true
    );
    return { lastMessages, scores };
  } catch (err) {
    console.log("err", err);
    return { lastMessages: [], scores: {} };
  }
};

/**
 * Makes an LLM request to the server
 */
export const llmRequest = async (
  prompt: { role: string; content: string }[],
  chatId: string,
  scenarioId: string,
  dontSaveUserMessage = false,
  onLimitReached?: () => void,
  freeTalkType: "custom" | "random" | "free"  = "free"
) => {
  console.log("prompt ", prompt);
  try {
    const response = await RequestHelper("post", `/v2/ai/prompt/llm`, {
      prompt,
      chatId,
      scenarioId,
      dontSaveUserMessage,
      freeTalkType,
    });
    if (response.hasReachedDailyLimit && onLimitReached) {
      onLimitReached();
    }

    return response;
  } catch (err) {
    console.log("err1", err);
    return { message: { content: "" }, messageId: "" };
  }
};

/**
 * Converts transcription results to chat history format
 */
export const getHistoryFromTranscription = (transcriptionResult: any[]) => {
  let history: { role: string; content: string }[] = [];
  const lastFortyChats = transcriptionResult.slice(-40);

  for (const chat of lastFortyChats) {
    if (chat.type !== "loading") {
      history.push({
        role: chat.type === "req" ? "user" : "assistant",
        content: chat.text,
      });
    }
  }

  return history;
};

/**
 * Checks grammar for a given text
 */
export const checkGrammar = async (text: string) => {
  try {
    const { result } = await RequestHelper("post", `/ai/grammer`, {
      text,
    });

    return result;
  } catch (err) {
    console.log(err);
    return { correct: true, corrected: text };
  }
};

/**
 * Gets translation for a given text
 */
export const getTranslation = async (text: string) => {
  try {
    const {
      message: { content },
    } = await RequestHelper("get", `/ai/prompt/translate/?q=${text}`);

    return content.trim();
  } catch (err) {
    console.log("Translation error:", err);
    return "";
  }
};
