import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../navigation/StackNavigator";
import styles from "../../styles/homeStyle";
import ConversationOptionsModal from "../modals/ConversationOptionsModal";

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const VoiceAssistantCard = () => {
  const navigation = useNavigation<NavigationProp>();
  const [modalVisible, setModalVisible] = useState(false);

  const handleOptionSelect = (freeTalkType: "custom" | "random" | "free") => {
    setModalVisible(false);
    navigation.navigate("freeTalk", {
      showVoiceAssistant: true,
      freeTalkType,
      freeTalkCustomScenario: ,
    });
  };

  return (
    <>
      <LinearGradient
        colors={["#2e8d8dff", "#1e7f7fff"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.classicLessonCard}
      >
        <View style={[styles.cardHeader]}>
          <Text style={styles.cardHeaderLabel}>مکالمه صوتی</Text>
          <Text style={styles.practiceSubtitle}>
            با معلم هوش مصنوعی صحبت کن
          </Text>
        </View>

        <TouchableOpacity
          style={styles.talkButton}
          onPress={() => setModalVisible(true)}
        >
          <Text style={styles.talkButtonText}>شروع مکالمه</Text>
        </TouchableOpacity>
      </LinearGradient>

      <ConversationOptionsModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onSelectOption={handleOptionSelect}
      />
    </>
  );
};

export default VoiceAssistantCard;
