import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Pressable,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialCommunityIcons, Ionicons } from '@expo/vector-icons';

interface ConversationOptionsModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectOption: (option: 'custom' | 'random' | 'free') => void;
}

const ConversationOptionsModal: React.FC<ConversationOptionsModalProps> = ({
  visible,
  onClose,
  onSelectOption,
}) => {
  const options = [
    {
      id: 'custom',
      title: 'موضوع دلخواه',
      subtitle: 'راجع به موضوع مورد علاقه‌ات صحبت کن',
      icon: 'heart',
      colors: ['#FF6B6B', '#FF8E8E'],
    },
    {
      id: 'random',
      title: 'موضوع تصادفی',
      subtitle: 'بذار ما یه موضوع جالب برات انتخاب کنیم',
      icon: 'dice-5',
      colors: ['#4ECDC4', '#44A08D'],
    },
    {
      id: 'free',
      title: 'گفت و گوی آزاد',
      subtitle: 'هر چی دوست داری بگو، ما گوش می‌دیم',
      icon: 'chat',
      colors: ['#667eea', '#764ba2'],
    },
  ];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalBackground}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>نوع مکالمه رو انتخاب کن</Text>
            <Pressable
              onPress={onClose}
              style={styles.closeButton}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons name="close" size={24} color="#666" />
            </Pressable>
          </View>

          {/* Options */}
          <View style={styles.optionsContainer}>
            {options.map((option, index) => (
              <TouchableOpacity
                key={option.id}
                onPress={() => onSelectOption(option.id as 'custom' | 'random' | 'free')}
                style={[styles.optionButton, { marginBottom: index === options.length - 1 ? 0 : 16 }]}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={option.colors}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.optionGradient}
                >
                  <View style={styles.optionContent}>
                    <View style={styles.iconContainer}>
                      <MaterialCommunityIcons
                        name={option.icon as any}
                        size={32}
                        color="white"
                      />
                    </View>
                    <View style={styles.textContainer}>
                      <Text style={styles.optionTitle}>{option.title}</Text>
                      <Text style={styles.optionSubtitle}>{option.subtitle}</Text>
                    </View>
                    <View style={styles.arrowContainer}>
                      <Ionicons name="chevron-forward" size={24} color="rgba(255,255,255,0.8)" />
                    </View>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    width: width * 0.9,
    maxWidth: 400,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'EstedadBold',
    color: '#333',
    flex: 1,
    textAlign: 'right',
  },
  closeButton: {
    padding: 4,
  },
  optionsContainer: {
    gap: 16,
  },
  optionButton: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  optionGradient: {
    padding: 20,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
  },
  textContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  optionTitle: {
    fontSize: 18,
    fontFamily: 'EstedadBold',
    color: 'white',
    marginBottom: 4,
    textAlign: 'right',
  },
  optionSubtitle: {
    fontSize: 14,
    fontFamily: 'EstedadRegular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'right',
    lineHeight: 20,
  },
  arrowContainer: {
    marginRight: 8,
  },
});

export default ConversationOptionsModal;
